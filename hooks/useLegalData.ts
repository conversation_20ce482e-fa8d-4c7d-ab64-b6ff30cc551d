import {CurrencyIcon, LegalIcon, PolicyIcon} from "@/components/ui/icon";
import {Paths} from "@/enum/Paths";
import {useTranslation} from "react-i18next";

export const useLegalData = () => {
    const {t} = useTranslation();
    return {
        title: "Legal and Privacy",
        content: [
            {
                icon: CurrencyIcon,
                title: t("account_legal_list_mist"),
                link: Paths.ACCOUNT_LEGAL_MIST,
            },
            {
                icon: PolicyIcon,
                title: t("account_legal_list_privacy"),
                link: Paths.ACCOUNT_LEGAL_PRIVACY,
            },
            {
                icon: LegalIcon,
                title: t("account_legal_list_terms"),
                link: Paths.ACCOUNT_LEGAL_TERMS,
            },
        ],
    };
};
