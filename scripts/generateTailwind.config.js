const fs = require("fs");

function hexToRgb(hex) {
    hex = hex.replace("#", "");
    if (hex.length === 8) hex = hex.slice(0, 6); // ucinamy alpha
    const bigint = parseInt(hex, 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;
    return `${r} ${g} ${b}`;
}

function convertColors(colors) {
    const vars = {};
    for (const [name, value] of Object.entries(colors)) {
        let normalized = name
            .replace(/^Primary-/, "primary-")
            .replace(/^Secondary-/, "secondary-")
            .replace(/^Tertiary-/, "tertiary-")
            .replace(/^Typography-/, "typography-")
            .replace(/^Background-/, "background-")
            .replace(/^Border-/, "outline-")
            .replace(/^Error-/, "error-")
            .replace(/^Success-/, "success-")
            .replace(/^Warning-/, "warning-")
            .replace(/^Info-/, "info-")
            .replace(/^Indicator-/, "indicator-")
            .toLowerCase();

        // Remove redundant repetition (e.g., "primary-primary100" -> "primary-100")
        normalized = normalized
            .replace(/^primary-primary/, "primary-")
            .replace(/^secondary-secondary/, "secondary-")
            .replace(/^tertiary-tertiary/, "tertiary-")
            .replace(/^typography-typography/, "typography-")
            .replace(/^background-background/, "background-")
            .replace(/^outline-border/, "outline-")
            .replace(/^error-error/, "error-")
            .replace(/^success-success/, "success-")
            .replace(/^warning-warning/, "warning-")
            .replace(/^info-info/, "info-")
            .replace(/^indicator-indicator/, "indicator-");

        vars[`--color-${normalized}`] = hexToRgb(value);
    }
    return vars;
}

function convertSpacing(spacing) {
    const vars = {};
    for (const [key, value] of Object.entries(spacing)) {
        vars[`--space-${key}`] = value;
    }
    return vars;
}

function convertFontSize(fontSize) {
    const vars = {};
    for (const [key, value] of Object.entries(fontSize)) {
        vars[`--font-size-${key}`] = Array.isArray(value) ? value[0] : value;
    }
    return vars;
}

function convertRadius(radii) {
    const vars = {};
    for (const [key, value] of Object.entries(radii)) {
        vars[`--radius-${key}`] = value;
    }
    return vars;
}

function convertShadow(shadows) {
    const vars = {};
    for (const [key, value] of Object.entries(shadows)) {
        vars[`--shadow-${key}`] = value;
    }
    return vars;
}

function buildVars(figmaExport) {
    return {
        ...convertColors(figmaExport.theme.extend.colors || {}),
        ...convertSpacing(figmaExport.theme.extend.spacing || {}),
        ...convertFontSize(figmaExport.theme.extend.fontSize || {}),
        ...convertRadius(figmaExport.theme.extend.borderRadius || {}),
        ...convertShadow(figmaExport.theme.extend.boxShadow || {}),
    };
}

// Merge multiple Tailwind-like exports into a single vars map
function buildVarsMany(...configs) {
    return Object.assign({}, ...configs.map(c => buildVars(c)));
}

const light = require("../tailwind/light.js");
const dark = require("../tailwind/dark.js");
const fixedLight = require("../tailwind/fixed-light.js");
const fixedDark = require("../tailwind/fixed-dark.js");
const mainMargin = require("../tailwind/main-margin.js");

const config = `
'use client';
import { vars } from 'nativewind';

export const config = {
  // light should include: light, fixedLight, mainMargin
  light: vars(${JSON.stringify(buildVarsMany(light, fixedLight, mainMargin), null, 2)}),
  // dark should include: dark, fixedDark, mainMargin
  dark: vars(${JSON.stringify(buildVarsMany(dark, fixedDark, mainMargin), null, 2)}),
};
`;

fs.writeFileSync("./components/ui/gluestack-ui-provider/config.ts", config);
console.log("✅ Glustack theme.config.ts generated with colors, spacing, fonts, radius, shadows!");
