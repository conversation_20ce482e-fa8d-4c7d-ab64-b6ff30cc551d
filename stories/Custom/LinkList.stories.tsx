// LinkList.stories.tsx
import {LinkList, LinkListItem} from "@/components/LinkList";
import {CurrencyIcon, LegalIcon, PolicyIcon} from "@/components/ui/icon";
import {Paths} from "@/enum/Paths";
import React from "react";
import {View} from "react-native";

export default {
    title: "Custom/LinkList",
    component: LinkList,
};

const LegalAndPrivacy = {
    title: "Legal and Privacy",
    content: [
        {
            icon: CurrencyIcon,
            title: "About Founding",
            link: Paths.ACCOUNT_LEGAL_MIST,
        },
        {
            icon: PolicyIcon,
            title: "Privacy Policy",
            link: Paths.ACCOUNT_LEGAL_PRIVACY,
        },
        {
            icon: LegalIcon,
            title: "Legal Information",
            link: Paths.ACCOUNT_LEGAL_TERMS,
        },
    ],
};

export const Default = () => (
    <View style={{padding: 20}}>
        <LinkList>
            {LegalAndPrivacy.content.map((item, index) => (
                <LinkListItem key={`legal-setting-${item.title}-${index}`} {...item} />
            ))}
        </LinkList>
    </View>
);
