// CircleBack.stories.tsx
import {CircleBack} from "@/components/Back";
import {Paths} from "@/enum/Paths";
import React from "react";
import {View} from "react-native";

export default {
    title: "Navigation/CircleBack",
    component: CircleBack,
};

export const Default = () => (
    <View style={{padding: 20}}>
        <CircleBack />
    </View>
);

export const WithRoute = () => (
    <View style={{padding: 20}}>
        <CircleBack to={Paths.ACCOUNT} />
    </View>
);
