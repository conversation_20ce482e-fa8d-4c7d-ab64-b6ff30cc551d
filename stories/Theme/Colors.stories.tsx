import React from "react";
import {ScrollView, Text, View} from "react-native";

export default {
    title: "Theme/Colors",
    component: View,
};

const ColorSwatch = ({name, className, description}: {name: string; className: string; description?: string}) => (
    <View className="flex-row items-center mb-2 p-2 rounded-md border border-outline-200">
        <View className={`w-12 h-12 rounded-md mr-4 ${className}`} />
        <View className="flex-1">
            <Text className="text-typography-900 font-semibold text-sm">{name}</Text>
            <Text className="text-typography-600 text-xs">{className}</Text>
            {description && <Text className="text-typography-500 text-xs mt-1">{description}</Text>}
        </View>
    </View>
);

const ColorPalette = ({
    title,
    colors,
}: {
    title: string;
    colors: Array<{name: string; className: string; description?: string}>;
}) => (
    <View className="mb-8">
        <Text className="text-typography-900 text-xl font-bold mb-4">{title}</Text>
        <View className="bg-background-50 p-4 rounded-lg">
            {colors.map((color, index) => (
                <ColorSwatch key={index} {...color} />
            ))}
        </View>
    </View>
);

export const Primary = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <ColorPalette
            title="Primary Colors"
            colors={[
                {
                    name: "Primary 0",
                    className: "bg-primary-0",
                    description: "Lightest primary shade",
                },
                {name: "Primary 50", className: "bg-primary-50"},
                {name: "Primary 100", className: "bg-primary-100"},
                {name: "Primary 200", className: "bg-primary-200"},
                {name: "Primary 300", className: "bg-primary-300"},
                {name: "Primary 400", className: "bg-primary-400"},
                {
                    name: "Primary 500",
                    className: "bg-primary-500",
                    description: "Main primary color",
                },
                {name: "Primary 600", className: "bg-primary-600"},
                {name: "Primary 700", className: "bg-primary-700"},
                {name: "Primary 800", className: "bg-primary-800"},
                {name: "Primary 900", className: "bg-primary-900"},
                {
                    name: "Primary 950",
                    className: "bg-primary-950",
                    description: "Darkest primary shade",
                },
            ]}
        />
    </ScrollView>
);

export const Secondary = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <ColorPalette
            title="Secondary Colors"
            colors={[
                {
                    name: "Secondary 0",
                    className: "bg-secondary-0",
                    description: "Lightest secondary shade",
                },
                {name: "Secondary 50", className: "bg-secondary-50"},
                {name: "Secondary 100", className: "bg-secondary-100"},
                {name: "Secondary 200", className: "bg-secondary-200"},
                {name: "Secondary 300", className: "bg-secondary-300"},
                {name: "Secondary 400", className: "bg-secondary-400"},
                {
                    name: "Secondary 500",
                    className: "bg-secondary-500",
                    description: "Main secondary color",
                },
                {name: "Secondary 600", className: "bg-secondary-600"},
                {name: "Secondary 700", className: "bg-secondary-700"},
                {name: "Secondary 800", className: "bg-secondary-800"},
                {name: "Secondary 900", className: "bg-secondary-900"},
                {
                    name: "Secondary 950",
                    className: "bg-secondary-950",
                    description: "Darkest secondary shade",
                },
            ]}
        />
    </ScrollView>
);

export const Tertiary = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <ColorPalette
            title="Tertiary Colors"
            colors={[
                {
                    name: "Tertiary 50",
                    className: "bg-tertiary-50",
                    description: "Lightest tertiary shade",
                },
                {name: "Tertiary 100", className: "bg-tertiary-100"},
                {name: "Tertiary 200", className: "bg-tertiary-200"},
                {name: "Tertiary 300", className: "bg-tertiary-300"},
                {name: "Tertiary 400", className: "bg-tertiary-400"},
                {
                    name: "Tertiary 500",
                    className: "bg-tertiary-500",
                    description: "Main tertiary color",
                },
                {name: "Tertiary 600", className: "bg-tertiary-600"},
                {name: "Tertiary 700", className: "bg-tertiary-700"},
                {name: "Tertiary 800", className: "bg-tertiary-800"},
                {name: "Tertiary 900", className: "bg-tertiary-900"},
                {
                    name: "Tertiary 950",
                    className: "bg-tertiary-950",
                    description: "Darkest tertiary shade",
                },
            ]}
        />
    </ScrollView>
);

export const Semantic = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <ColorPalette
            title="Error Colors"
            colors={[
                {name: "Error 0", className: "bg-error-0"},
                {name: "Error 50", className: "bg-error-50"},
                {name: "Error 100", className: "bg-error-100"},
                {name: "Error 200", className: "bg-error-200"},
                {name: "Error 300", className: "bg-error-300"},
                {name: "Error 400", className: "bg-error-400"},
                {
                    name: "Error 500",
                    className: "bg-error-500",
                    description: "Main error color",
                },
                {name: "Error 600", className: "bg-error-600"},
                {name: "Error 700", className: "bg-error-700"},
                {name: "Error 800", className: "bg-error-800"},
                {name: "Error 900", className: "bg-error-900"},
                {name: "Error 950", className: "bg-error-950"},
            ]}
        />

        <ColorPalette
            title="Success Colors"
            colors={[
                {name: "Success 0", className: "bg-success-0"},
                {name: "Success 50", className: "bg-success-50"},
                {name: "Success 100", className: "bg-success-100"},
                {name: "Success 200", className: "bg-success-200"},
                {name: "Success 300", className: "bg-success-300"},
                {name: "Success 400", className: "bg-success-400"},
                {
                    name: "Success 500",
                    className: "bg-success-500",
                    description: "Main success color",
                },
                {name: "Success 600", className: "bg-success-600"},
                {name: "Success 700", className: "bg-success-700"},
                {name: "Success 800", className: "bg-success-800"},
                {name: "Success 900", className: "bg-success-900"},
                {name: "Success 950", className: "bg-success-950"},
            ]}
        />
    </ScrollView>
);

export const Warning = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <ColorPalette
            title="Warning Colors"
            colors={[
                {name: "Warning 0", className: "bg-warning-0"},
                {name: "Warning 50", className: "bg-warning-50"},
                {name: "Warning 100", className: "bg-warning-100"},
                {name: "Warning 200", className: "bg-warning-200"},
                {name: "Warning 300", className: "bg-warning-300"},
                {name: "Warning 400", className: "bg-warning-400"},
                {
                    name: "Warning 500",
                    className: "bg-warning-500",
                    description: "Main warning color",
                },
                {name: "Warning 600", className: "bg-warning-600"},
                {name: "Warning 700", className: "bg-warning-700"},
                {name: "Warning 800", className: "bg-warning-800"},
                {name: "Warning 900", className: "bg-warning-900"},
                {name: "Warning 950", className: "bg-warning-950"},
            ]}
        />

        <ColorPalette
            title="Info Colors"
            colors={[
                {name: "Info 0", className: "bg-info-0"},
                {name: "Info 50", className: "bg-info-50"},
                {name: "Info 100", className: "bg-info-100"},
                {name: "Info 200", className: "bg-info-200"},
                {name: "Info 300", className: "bg-info-300"},
                {name: "Info 400", className: "bg-info-400"},
                {
                    name: "Info 500",
                    className: "bg-info-500",
                    description: "Main info color",
                },
                {name: "Info 600", className: "bg-info-600"},
                {name: "Info 700", className: "bg-info-700"},
                {name: "Info 800", className: "bg-info-800"},
                {name: "Info 900", className: "bg-info-900"},
                {name: "Info 950", className: "bg-info-950"},
            ]}
        />
    </ScrollView>
);

export const Typography = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <ColorPalette
            title="Typography Colors"
            colors={[
                {
                    name: "Typography 0",
                    className: "bg-typography-0",
                    description: "Lightest text color",
                },
                {name: "Typography 50", className: "bg-typography-50"},
                {name: "Typography 100", className: "bg-typography-100"},
                {name: "Typography 200", className: "bg-typography-200"},
                {name: "Typography 300", className: "bg-typography-300"},
                {name: "Typography 400", className: "bg-typography-400"},
                {name: "Typography 500", className: "bg-typography-500"},
                {name: "Typography 600", className: "bg-typography-600"},
                {
                    name: "Typography 700",
                    className: "bg-typography-700",
                    description: "Main text color",
                },
                {name: "Typography 800", className: "bg-typography-800"},
                {name: "Typography 900", className: "bg-typography-900"},
                {
                    name: "Typography 950",
                    className: "bg-typography-950",
                    description: "Darkest text color",
                },
                {name: "Typography White", className: "bg-typography-white"},
                {name: "Typography Gray", className: "bg-typography-gray"},
                {name: "Typography Black", className: "bg-typography-black"},
            ]}
        />
    </ScrollView>
);

export const Background = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <ColorPalette
            title="Background Colors"
            colors={[
                {
                    name: "Background 0",
                    className: "bg-background-0",
                    description: "Main background",
                },
                {name: "Background 50", className: "bg-background-50"},
                {name: "Background 100", className: "bg-background-100"},
                {name: "Background 200", className: "bg-background-200"},
                {name: "Background 300", className: "bg-background-300"},
                {name: "Background 400", className: "bg-background-400"},
                {name: "Background 500", className: "bg-background-500"},
                {name: "Background 600", className: "bg-background-600"},
                {name: "Background 700", className: "bg-background-700"},
                {name: "Background 800", className: "bg-background-800"},
                {name: "Background 900", className: "bg-background-900"},
                {name: "Background 950", className: "bg-background-950"},
                {name: "Background Error", className: "bg-background-error"},
                {name: "Background Warning", className: "bg-background-warning"},
                {name: "Background Muted", className: "bg-background-muted"},
                {name: "Background Success", className: "bg-background-success"},
                {name: "Background Info", className: "bg-background-info"},
                {name: "Background White", className: "bg-background-white"},
                {name: "Background Black", className: "bg-background-black"},
                {name: "Background Light", className: "bg-background-light"},
                {name: "Background Dark", className: "bg-background-dark"},
            ]}
        />
    </ScrollView>
);

export const Outline = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <ColorPalette
            title="Outline Colors"
            colors={[
                {
                    name: "Outline 0",
                    className: "bg-outline-0",
                    description: "Lightest border color",
                },
                {name: "Outline 50", className: "bg-outline-50"},
                {name: "Outline 100", className: "bg-outline-100"},
                {name: "Outline 200", className: "bg-outline-200"},
                {name: "Outline 300", className: "bg-outline-300"},
                {name: "Outline 400", className: "bg-outline-400"},
                {
                    name: "Outline 500",
                    className: "bg-outline-500",
                    description: "Main border color",
                },
                {name: "Outline 600", className: "bg-outline-600"},
                {name: "Outline 700", className: "bg-outline-700"},
                {name: "Outline 800", className: "bg-outline-800"},
                {name: "Outline 900", className: "bg-outline-900"},
                {
                    name: "Outline 950",
                    className: "bg-outline-950",
                    description: "Darkest border color",
                },
            ]}
        />
    </ScrollView>
);

export const Indicator = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <ColorPalette
            title="Indicator Colors"
            colors={[
                {
                    name: "Indicator Primary",
                    className: "bg-indicator-primary",
                    description: "Primary indicator",
                },
                {
                    name: "Indicator Info",
                    className: "bg-indicator-info",
                    description: "Info indicator",
                },
                {
                    name: "Indicator Error",
                    className: "bg-indicator-error",
                    description: "Error indicator",
                },
            ]}
        />

        <ColorPalette
            title="Label Colors"
            colors={[
                {
                    name: "Label White",
                    className: "bg-label-white",
                    description: "White label",
                },
                {
                    name: "Label Gray",
                    className: "bg-label-gray",
                    description: "Gray label",
                },
                {
                    name: "Label Black",
                    className: "bg-label-black",
                    description: "Black label",
                },
            ]}
        />
    </ScrollView>
);
