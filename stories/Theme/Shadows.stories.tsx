import React from "react";
import {View, Text, ScrollView} from "react-native";

export default {
    title: "Theme/Shadows",
    component: View,
};

const ShadowExample = ({
    title,
    className,
    description,
    shadowValue,
}: {
    title: string;
    className: string;
    description?: string;
    shadowValue?: string;
}) => (
    <View className="mb-6 p-6 bg-background-50 rounded-lg">
        <View className="flex-row items-center justify-between mb-4">
            <Text className="text-typography-900 font-semibold text-sm">{title}</Text>
            <Text className="text-typography-600 text-xs">{className}</Text>
        </View>

        <View className="items-center py-8">
            <View className={`bg-background-0 p-6 rounded-lg ${className}`}>
                <Text className="text-typography-700 text-center font-medium">Sample Card</Text>
                <Text className="text-typography-500 text-center text-sm mt-1">With shadow effect</Text>
            </View>
        </View>

        {description && <Text className="text-typography-500 text-xs mt-2">{description}</Text>}

        {shadowValue && (
            <View className="mt-2 p-2 bg-background-100 rounded">
                <Text className="text-typography-600 text-xs font-mono">{shadowValue}</Text>
            </View>
        )}
    </View>
);

export const HardShadows = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Hard Shadows</Text>
        <Text className="text-typography-600 text-sm mb-6">
            Hard shadows create defined, crisp shadow effects with clear edges.
        </Text>

        <ShadowExample
            title="Hard Shadow 1"
            className="shadow-hard-1"
            description="Left-bottom shadow - creates depth with directional lighting"
            shadowValue="-2px 2px 8px 0px rgba(38, 38, 38, 0.20)"
        />

        <ShadowExample
            title="Hard Shadow 2"
            className="shadow-hard-2"
            description="Bottom shadow - standard drop shadow effect"
            shadowValue="0px 3px 10px 0px rgba(38, 38, 38, 0.20)"
        />

        <ShadowExample
            title="Hard Shadow 3"
            className="shadow-hard-3"
            description="Right-bottom shadow - creates depth with opposite directional lighting"
            shadowValue="2px 2px 8px 0px rgba(38, 38, 38, 0.20)"
        />

        <ShadowExample
            title="Hard Shadow 4"
            className="shadow-hard-4"
            description="Top shadow - creates lifted effect"
            shadowValue="0px -3px 10px 0px rgba(38, 38, 38, 0.20)"
        />

        <ShadowExample
            title="Hard Shadow 5"
            className="shadow-hard-5"
            description="Subtle bottom shadow - lighter version for subtle elevation"
            shadowValue="0px 2px 10px 0px rgba(38, 38, 38, 0.10)"
        />
    </ScrollView>
);

export const SoftShadows = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Soft Shadows</Text>
        <Text className="text-typography-600 text-sm mb-6">
            Soft shadows create diffused, blurred shadow effects for a more natural appearance.
        </Text>

        <ShadowExample
            title="Soft Shadow 1"
            className="shadow-soft-1"
            description="Small soft shadow - subtle elevation for cards and buttons"
            shadowValue="0px 0px 10px rgba(38, 38, 38, 0.1)"
        />

        <ShadowExample
            title="Soft Shadow 2"
            className="shadow-soft-2"
            description="Medium soft shadow - moderate elevation for modals and overlays"
            shadowValue="0px 0px 20px rgba(38, 38, 38, 0.2)"
        />

        <ShadowExample
            title="Soft Shadow 3"
            className="shadow-soft-3"
            description="Large soft shadow - high elevation for floating elements"
            shadowValue="0px 0px 30px rgba(38, 38, 38, 0.1)"
        />

        <ShadowExample
            title="Soft Shadow 4"
            className="shadow-soft-4"
            description="Extra large soft shadow - maximum elevation for hero elements"
            shadowValue="0px 0px 40px rgba(38, 38, 38, 0.1)"
        />
    </ScrollView>
);

export const ShadowComparisons = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Shadow Comparisons</Text>

        <View className="mb-8">
            <Text className="text-typography-800 text-lg font-semibold mb-4">Elevation Levels</Text>
            <Text className="text-typography-600 text-sm mb-4">
                Different shadow intensities to show elevation hierarchy
            </Text>

            <View className="flex-row flex-wrap justify-between">
                <View className="w-[48%] mb-4">
                    <View className="bg-background-0 p-4 rounded-lg shadow-soft-1">
                        <Text className="text-typography-700 text-center text-sm">Level 1</Text>
                        <Text className="text-typography-500 text-center text-xs">Soft 1</Text>
                    </View>
                </View>

                <View className="w-[48%] mb-4">
                    <View className="bg-background-0 p-4 rounded-lg shadow-soft-2">
                        <Text className="text-typography-700 text-center text-sm">Level 2</Text>
                        <Text className="text-typography-500 text-center text-xs">Soft 2</Text>
                    </View>
                </View>

                <View className="w-[48%] mb-4">
                    <View className="bg-background-0 p-4 rounded-lg shadow-soft-3">
                        <Text className="text-typography-700 text-center text-sm">Level 3</Text>
                        <Text className="text-typography-500 text-center text-xs">Soft 3</Text>
                    </View>
                </View>

                <View className="w-[48%] mb-4">
                    <View className="bg-background-0 p-4 rounded-lg shadow-soft-4">
                        <Text className="text-typography-700 text-center text-sm">Level 4</Text>
                        <Text className="text-typography-500 text-center text-xs">Soft 4</Text>
                    </View>
                </View>
            </View>
        </View>

        <View className="mb-8">
            <Text className="text-typography-800 text-lg font-semibold mb-4">Hard vs Soft</Text>
            <Text className="text-typography-600 text-sm mb-4">Comparison between hard and soft shadow styles</Text>

            <View className="flex-row justify-between">
                <View className="w-[48%]">
                    <View className="bg-background-0 p-4 rounded-lg shadow-hard-2">
                        <Text className="text-typography-700 text-center text-sm font-medium">Hard Shadow</Text>
                        <Text className="text-typography-500 text-center text-xs">Defined edges</Text>
                    </View>
                </View>

                <View className="w-[48%]">
                    <View className="bg-background-0 p-4 rounded-lg shadow-soft-2">
                        <Text className="text-typography-700 text-center text-sm font-medium">Soft Shadow</Text>
                        <Text className="text-typography-500 text-center text-xs">Blurred edges</Text>
                    </View>
                </View>
            </View>
        </View>
    </ScrollView>
);

export const ShadowUseCases = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Shadow Use Cases</Text>

        <View className="mb-6">
            <Text className="text-typography-800 text-lg font-semibold mb-3">Card Components</Text>
            <View className="bg-background-0 p-4 rounded-lg shadow-soft-1">
                <Text className="text-typography-900 font-semibold mb-2">Product Card</Text>
                <Text className="text-typography-600 text-sm">
                    Cards typically use soft-1 or soft-2 shadows for subtle elevation
                </Text>
            </View>
        </View>

        <View className="mb-6">
            <Text className="text-typography-800 text-lg font-semibold mb-3">Modal Dialogs</Text>
            <View className="bg-background-0 p-6 rounded-lg shadow-soft-3">
                <Text className="text-typography-900 font-semibold mb-2">Modal Title</Text>
                <Text className="text-typography-600 text-sm">
                    Modals use stronger shadows (soft-3 or soft-4) to appear above other content
                </Text>
            </View>
        </View>

        <View className="mb-6">
            <Text className="text-typography-800 text-lg font-semibold mb-3">Buttons</Text>
            <View className="flex-row gap-4">
                <View className="bg-primary-500 px-4 py-2 rounded-md shadow-hard-2">
                    <Text className="text-typography-white text-center font-medium">Primary</Text>
                </View>
                <View className="bg-background-0 px-4 py-2 rounded-md shadow-soft-1 border border-outline-300">
                    <Text className="text-typography-700 text-center font-medium">Secondary</Text>
                </View>
            </View>
            <Text className="text-typography-500 text-xs mt-2">
                Buttons can use hard shadows for definition or soft shadows for subtlety
            </Text>
        </View>

        <View className="mb-6">
            <Text className="text-typography-800 text-lg font-semibold mb-3">Floating Action Button</Text>
            <View className="items-center">
                <View className="bg-primary-500 w-14 h-14 rounded-full shadow-soft-4 items-center justify-center">
                    <Text className="text-typography-white font-bold text-lg">+</Text>
                </View>
            </View>
            <Text className="text-typography-500 text-xs mt-2 text-center">
                FABs use the strongest shadows (soft-4) to appear floating above all content
            </Text>
        </View>
    </ScrollView>
);
