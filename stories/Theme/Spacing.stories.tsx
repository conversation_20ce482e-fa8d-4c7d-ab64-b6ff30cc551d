import React from "react";
import {View, Text, ScrollView} from "react-native";

export default {
    title: "Theme/Spacing",
    component: View,
};

const SpacingExample = ({
    title,
    spacingClass,
    value,
    type = "padding",
    description,
}: {
    title: string;
    spacingClass: string;
    value: string;
    type?: "padding" | "margin" | "gap";
    description?: string;
}) => (
    <View className="mb-4 p-4 bg-background-50 rounded-lg">
        <View className="flex-row items-center justify-between mb-2">
            <Text className="text-typography-900 font-semibold text-sm">{title}</Text>
            <Text className="text-typography-600 text-xs">
                {spacingClass} ({value})
            </Text>
        </View>

        <View className="bg-background-100 p-2 rounded">
            {type === "padding" && (
                <View className={`bg-primary-100 ${spacingClass}`}>
                    <View className="bg-primary-500 p-2 rounded">
                        <Text className="text-typography-white text-xs text-center">Content</Text>
                    </View>
                </View>
            )}

            {type === "margin" && (
                <View className="bg-primary-100 p-2 rounded">
                    <View className={`bg-primary-500 p-2 rounded ${spacingClass}`}>
                        <Text className="text-typography-white text-xs text-center">Content</Text>
                    </View>
                </View>
            )}

            {type === "gap" && (
                <View className={`flex-row ${spacingClass} bg-primary-100 p-2 rounded`}>
                    <View className="bg-primary-500 p-2 rounded flex-1">
                        <Text className="text-typography-white text-xs text-center">Item 1</Text>
                    </View>
                    <View className="bg-primary-500 p-2 rounded flex-1">
                        <Text className="text-typography-white text-xs text-center">Item 2</Text>
                    </View>
                </View>
            )}
        </View>

        {description && <Text className="text-typography-500 text-xs mt-2">{description}</Text>}
    </View>
);

export const Padding = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Padding</Text>

        <SpacingExample title="Padding 0" spacingClass="p-0" value="0px" type="padding" description="No padding" />

        <SpacingExample
            title="Padding px"
            spacingClass="p-px"
            value="1px"
            type="padding"
            description="1 pixel padding"
        />

        <SpacingExample
            title="Padding 0.5"
            spacingClass="p-0.5"
            value="2px"
            type="padding"
            description="Half unit padding"
        />

        <SpacingExample title="Padding 1" spacingClass="p-1" value="4px" type="padding" description="1 unit padding" />

        <SpacingExample
            title="Padding 1.5"
            spacingClass="p-1.5"
            value="6px"
            type="padding"
            description="1.5 unit padding"
        />

        <SpacingExample title="Padding 2" spacingClass="p-2" value="8px" type="padding" description="2 unit padding" />

        <SpacingExample
            title="Padding 2.5"
            spacingClass="p-2.5"
            value="10px"
            type="padding"
            description="2.5 unit padding"
        />

        <SpacingExample title="Padding 3" spacingClass="p-3" value="12px" type="padding" description="3 unit padding" />

        <SpacingExample
            title="Padding 4"
            spacingClass="p-4"
            value="16px"
            type="padding"
            description="4 unit padding - common default"
        />

        <SpacingExample title="Padding 5" spacingClass="p-5" value="20px" type="padding" description="5 unit padding" />

        <SpacingExample title="Padding 6" spacingClass="p-6" value="24px" type="padding" description="6 unit padding" />

        <SpacingExample title="Padding 8" spacingClass="p-8" value="32px" type="padding" description="8 unit padding" />

        <SpacingExample
            title="Padding 10"
            spacingClass="p-10"
            value="40px"
            type="padding"
            description="10 unit padding"
        />

        <SpacingExample
            title="Padding 12"
            spacingClass="p-12"
            value="48px"
            type="padding"
            description="12 unit padding"
        />

        <SpacingExample
            title="Padding 16"
            spacingClass="p-16"
            value="64px"
            type="padding"
            description="16 unit padding"
        />

        <SpacingExample
            title="Padding 20"
            spacingClass="p-20"
            value="80px"
            type="padding"
            description="20 unit padding"
        />

        <SpacingExample
            title="Padding 24"
            spacingClass="p-24"
            value="96px"
            type="padding"
            description="24 unit padding"
        />
    </ScrollView>
);

export const Margin = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Margin</Text>

        <SpacingExample title="Margin 0" spacingClass="m-0" value="0px" type="margin" description="No margin" />

        <SpacingExample title="Margin 1" spacingClass="m-1" value="4px" type="margin" description="1 unit margin" />

        <SpacingExample title="Margin 2" spacingClass="m-2" value="8px" type="margin" description="2 unit margin" />

        <SpacingExample title="Margin 3" spacingClass="m-3" value="12px" type="margin" description="3 unit margin" />

        <SpacingExample
            title="Margin 4"
            spacingClass="m-4"
            value="16px"
            type="margin"
            description="4 unit margin - common default"
        />

        <SpacingExample title="Margin 5" spacingClass="m-5" value="20px" type="margin" description="5 unit margin" />

        <SpacingExample title="Margin 6" spacingClass="m-6" value="24px" type="margin" description="6 unit margin" />

        <SpacingExample title="Margin 8" spacingClass="m-8" value="32px" type="margin" description="8 unit margin" />

        <SpacingExample title="Margin 10" spacingClass="m-10" value="40px" type="margin" description="10 unit margin" />

        <SpacingExample title="Margin 12" spacingClass="m-12" value="48px" type="margin" description="12 unit margin" />
    </ScrollView>
);

export const Gap = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Gap (Flexbox)</Text>

        <SpacingExample title="Gap 0" spacingClass="gap-0" value="0px" type="gap" description="No gap between items" />

        <SpacingExample title="Gap 1" spacingClass="gap-1" value="4px" type="gap" description="1 unit gap" />

        <SpacingExample title="Gap 2" spacingClass="gap-2" value="8px" type="gap" description="2 unit gap" />

        <SpacingExample title="Gap 3" spacingClass="gap-3" value="12px" type="gap" description="3 unit gap" />

        <SpacingExample
            title="Gap 4"
            spacingClass="gap-4"
            value="16px"
            type="gap"
            description="4 unit gap - common default"
        />

        <SpacingExample title="Gap 5" spacingClass="gap-5" value="20px" type="gap" description="5 unit gap" />

        <SpacingExample title="Gap 6" spacingClass="gap-6" value="24px" type="gap" description="6 unit gap" />

        <SpacingExample title="Gap 8" spacingClass="gap-8" value="32px" type="gap" description="8 unit gap" />
    </ScrollView>
);

export const LargeSpacing = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Large Spacing Values</Text>

        <SpacingExample
            title="Padding 32"
            spacingClass="p-32"
            value="128px"
            type="padding"
            description="32 unit padding - large sections"
        />

        <SpacingExample
            title="Padding 40"
            spacingClass="p-40"
            value="160px"
            type="padding"
            description="40 unit padding - very large sections"
        />

        <SpacingExample
            title="Padding 48"
            spacingClass="p-48"
            value="192px"
            type="padding"
            description="48 unit padding - hero sections"
        />

        <SpacingExample
            title="Padding 56"
            spacingClass="p-56"
            value="224px"
            type="padding"
            description="56 unit padding - extra large sections"
        />

        <SpacingExample
            title="Padding 64"
            spacingClass="p-64"
            value="256px"
            type="padding"
            description="64 unit padding - massive sections"
        />

        <SpacingExample
            title="Padding 72"
            spacingClass="p-72"
            value="288px"
            type="padding"
            description="72 unit padding - huge sections"
        />

        <SpacingExample
            title="Padding 80"
            spacingClass="p-80"
            value="320px"
            type="padding"
            description="80 unit padding - enormous sections"
        />

        <SpacingExample
            title="Padding 96"
            spacingClass="p-96"
            value="384px"
            type="padding"
            description="96 unit padding - maximum spacing"
        />
    </ScrollView>
);
