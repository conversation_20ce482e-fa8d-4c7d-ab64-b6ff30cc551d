import React from "react";
import {ScrollView, Text, View} from "react-native";

export default {
    title: "Theme/Borders",
    component: View,
};

const BorderExample = ({
    title,
    className,
    value,
    description,
}: {
    title: string;
    className: string;
    value: string;
    description?: string;
}) => (
    <View className="mb-4 p-4 bg-background-50 rounded-lg">
        <View className="flex-row items-center justify-between mb-3">
            <Text className="text-typography-900 font-semibold text-sm">{title}</Text>
            <Text className="text-typography-600 text-xs">
                {className} ({value})
            </Text>
        </View>

        <View className={`bg-background-0 p-4 ${className} border-outline-500`}>
            <Text className="text-typography-700 text-center">Sample Content</Text>
        </View>

        {description && <Text className="text-typography-500 text-xs mt-2">{description}</Text>}
    </View>
);

const BorderRadiusExample = ({
    title,
    className,
    value,
    description,
}: {
    title: string;
    className: string;
    value: string;
    description?: string;
}) => (
    <View className="mb-4 p-4 bg-background-50 rounded-lg">
        <View className="flex-row items-center justify-between mb-3">
            <Text className="text-typography-900 font-semibold text-sm">{title}</Text>
            <Text className="text-typography-600 text-xs">
                {className} ({value})
            </Text>
        </View>

        <View className={`bg-primary-500 p-4 border-2 border-outline-500 ${className}`}>
            <Text className="text-typography-white text-center font-medium">Sample Content</Text>
        </View>

        {description && <Text className="text-typography-500 text-xs mt-2">{description}</Text>}
    </View>
);

const BorderColorExample = ({
    title,
    className,
    description,
}: {
    title: string;
    className: string;
    description?: string;
}) => (
    <View className="mb-4 p-4 bg-background-50 rounded-lg">
        <View className="flex-row items-center justify-between mb-3">
            <Text className="text-typography-900 font-semibold text-sm">{title}</Text>
            <Text className="text-typography-600 text-xs">{className}</Text>
        </View>

        <View className={`bg-background-0 p-4 border-2 rounded-md ${className}`}>
            <Text className="text-typography-700 text-center">Sample Content</Text>
        </View>

        {description && <Text className="text-typography-500 text-xs mt-2">{description}</Text>}
    </View>
);

export const BorderWidths = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Border Widths</Text>

        <BorderExample title="Border 0" className="" value="0px" description="No border" />

        <BorderExample title="Border 1" className="border-1" value="1px" description="1 pixel border - most common" />

        <BorderExample
            title="Border 2"
            className="border-2"
            value="2px"
            description="2 pixel border - medium emphasis"
        />

        <BorderExample
            title="Border 4"
            className="border-4"
            value="4px"
            description="4 pixel border - strong emphasis"
        />

        <BorderExample
            title="Border 8"
            className="border-8"
            value="8px"
            description="8 pixel border - very strong emphasis"
        />
    </ScrollView>
);

export const BorderRadius = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Border Radius</Text>

        <BorderRadiusExample
            title="Rounded None"
            className="rounded-none"
            value="0px"
            description="No border radius - sharp corners"
        />

        <BorderRadiusExample
            title="Rounded XS"
            className="rounded-xs"
            value="2px"
            description="Extra small border radius"
        />

        <BorderRadiusExample title="Rounded SM" className="rounded-sm" value="4px" description="Small border radius" />

        <BorderRadiusExample
            title="Rounded MD"
            className="rounded-md"
            value="6px"
            description="Medium border radius - common default"
        />

        <BorderRadiusExample title="Rounded LG" className="rounded-lg" value="8px" description="Large border radius" />

        <BorderRadiusExample
            title="Rounded XL"
            className="rounded-xl"
            value="12px"
            description="Extra large border radius"
        />

        <BorderRadiusExample
            title="Rounded 2XL"
            className="rounded-2xl"
            value="16px"
            description="2X large border radius"
        />

        <BorderRadiusExample
            title="Rounded 3XL"
            className="rounded-3xl"
            value="24px"
            description="3X large border radius"
        />

        <BorderRadiusExample
            title="Rounded Full"
            className="rounded-full"
            value="9999px"
            description="Full border radius - perfect circle/pill shape"
        />
    </ScrollView>
);

export const BorderColors = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Border Colors</Text>

        <Text className="text-typography-800 text-lg font-semibold mb-4">Primary Colors</Text>
        <BorderColorExample title="Primary 300" className="border-primary-300" description="Light primary border" />
        <BorderColorExample title="Primary 500" className="border-primary-500" description="Main primary border" />
        <BorderColorExample title="Primary 700" className="border-primary-700" description="Dark primary border" />

        <Text className="text-typography-800 text-lg font-semibold mb-4 mt-6">Secondary Colors</Text>
        <BorderColorExample
            title="Secondary 300"
            className="border-secondary-300"
            description="Light secondary border"
        />
        <BorderColorExample
            title="Secondary 500"
            className="border-secondary-500"
            description="Main secondary border"
        />
        <BorderColorExample
            title="Secondary 700"
            className="border-secondary-700"
            description="Dark secondary border"
        />

        <Text className="text-typography-800 text-lg font-semibold mb-4 mt-6">Outline Colors</Text>
        <BorderColorExample
            title="Outline 200"
            className="border-outline-200"
            description="Very light outline border"
        />
        <BorderColorExample title="Outline 300" className="border-outline-300" description="Light outline border" />
        <BorderColorExample
            title="Outline 500"
            className="border-outline-500"
            description="Main outline border - most common"
        />
        <BorderColorExample title="Outline 700" className="border-outline-700" description="Dark outline border" />
    </ScrollView>
);

export const SemanticBorders = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Semantic Border Colors</Text>

        <BorderColorExample title="Error Border" className="border-error-500" description="Error state border - red" />

        <BorderColorExample
            title="Success Border"
            className="border-success-500"
            description="Success state border - green"
        />

        <BorderColorExample
            title="Warning Border"
            className="border-warning-500"
            description="Warning state border - yellow/orange"
        />

        <BorderColorExample title="Info Border" className="border-info-500" description="Info state border - blue" />
    </ScrollView>
);

export const BorderCombinations = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Border Combinations</Text>

        <View className="mb-4 p-4 bg-background-50 rounded-lg">
            <Text className="text-typography-900 font-semibold text-sm mb-3">Card Style</Text>
            <View className="bg-background-0 p-4 border-1 border-outline-200 rounded-lg">
                <Text className="text-typography-700">Subtle card with light border and rounded corners</Text>
            </View>
            <Text className="text-typography-500 text-xs mt-2">border-1 border-outline-200 rounded-lg</Text>
        </View>

        <View className="mb-4 p-4 bg-background-50 rounded-lg">
            <Text className="text-typography-900 font-semibold text-sm mb-3">Button Style</Text>
            <View className="bg-primary-500 p-3 border-2 border-primary-600 rounded-md">
                <Text className="text-typography-white text-center font-medium">Primary Button</Text>
            </View>
            <Text className="text-typography-500 text-xs mt-2">border-2 border-primary-600 rounded-md</Text>
        </View>

        <View className="mb-4 p-4 bg-background-50 rounded-lg">
            <Text className="text-typography-900 font-semibold text-sm mb-3">Input Style</Text>
            <View className="bg-background-0 p-3 border-1 border-outline-300 rounded-sm">
                <Text className="text-typography-600">Input field placeholder</Text>
            </View>
            <Text className="text-typography-500 text-xs mt-2">border-1 border-outline-300 rounded-sm</Text>
        </View>

        <View className="mb-4 p-4 bg-background-50 rounded-lg">
            <Text className="text-typography-900 font-semibold text-sm mb-3">Alert Style</Text>
            <View className="bg-error-50 p-4 border-1 border-error-300 rounded-lg">
                <Text className="text-error-800">Error message with matching border</Text>
            </View>
            <Text className="text-typography-500 text-xs mt-2">border-1 border-error-300 rounded-lg</Text>
        </View>
    </ScrollView>
);
