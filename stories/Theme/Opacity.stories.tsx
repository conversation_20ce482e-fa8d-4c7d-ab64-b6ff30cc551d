import React from "react";
import {View, Text, ScrollView} from "react-native";

export default {
    title: "Theme/Opacity",
    component: View,
};

const OpacityExample = ({
    title,
    className,
    value,
    description,
}: {
    title: string;
    className: string;
    value: string;
    description?: string;
}) => (
    <View className="mb-4 p-4 bg-background-50 rounded-lg">
        <View className="flex-row items-center justify-between mb-3">
            <Text className="text-typography-900 font-semibold text-sm">{title}</Text>
            <Text className="text-typography-600 text-xs">
                {className} ({value})
            </Text>
        </View>

        <View className="bg-primary-100 p-4 rounded-lg relative">
            <View className={`bg-primary-500 p-4 rounded-md ${className}`}>
                <Text className="text-typography-white text-center font-medium">Sample Content</Text>
            </View>

            {/* Background pattern to show transparency */}
            <View className="absolute inset-0 opacity-20">
                <View className="flex-row h-full">
                    <View className="flex-1 bg-secondary-300" />
                    <View className="flex-1 bg-secondary-500" />
                </View>
            </View>
        </View>

        {description && <Text className="text-typography-500 text-xs mt-2">{description}</Text>}
    </View>
);

const OverlayExample = ({
    title,
    className,
    value,
    description,
}: {
    title: string;
    className: string;
    value: string;
    description?: string;
}) => (
    <View className="mb-4 p-4 bg-background-50 rounded-lg">
        <View className="flex-row items-center justify-between mb-3">
            <Text className="text-typography-900 font-semibold text-sm">{title}</Text>
            <Text className="text-typography-600 text-xs">
                {className} ({value})
            </Text>
        </View>

        <View className="relative h-24 bg-gradient-to-r from-primary-400 to-secondary-400 rounded-lg overflow-hidden">
            {/* Background content */}
            <View className="absolute inset-0 items-center justify-center">
                <Text className="text-typography-white font-bold text-lg">Background</Text>
            </View>

            {/* Overlay */}
            <View className={`absolute inset-0 bg-background-900 ${className} items-center justify-center`}>
                <Text className="text-typography-white font-medium">Overlay</Text>
            </View>
        </View>

        {description && <Text className="text-typography-500 text-xs mt-2">{description}</Text>}
    </View>
);

export const BasicOpacity = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Basic Opacity</Text>
        <Text className="text-typography-600 text-sm mb-6">
            Opacity values control the transparency of elements. Lower values are more transparent.
        </Text>

        <OpacityExample
            title="Opacity 0"
            className="opacity-0"
            value="0%"
            description="Completely transparent - invisible"
        />

        <OpacityExample
            title="Opacity 5"
            className="opacity-5"
            value="5%"
            description="Almost transparent - barely visible"
        />

        <OpacityExample
            title="Opacity 10"
            className="opacity-10"
            value="10%"
            description="Very transparent - subtle presence"
        />

        <OpacityExample
            title="Opacity 20"
            className="opacity-20"
            value="20%"
            description="Highly transparent - light overlay effect"
        />

        <OpacityExample
            title="Opacity 25"
            className="opacity-25"
            value="25%"
            description="Quarter opacity - common for disabled states"
        />

        <OpacityExample
            title="Opacity 30"
            className="opacity-30"
            value="30%"
            description="Low opacity - subtle background elements"
        />

        <OpacityExample
            title="Opacity 40"
            className="opacity-40"
            value="40%"
            description="Medium-low opacity - muted elements"
        />

        <OpacityExample
            title="Opacity 50"
            className="opacity-50"
            value="50%"
            description="Half opacity - balanced transparency"
        />

        <OpacityExample
            title="Opacity 60"
            className="opacity-60"
            value="60%"
            description="Medium-high opacity - visible but subdued"
        />

        <OpacityExample
            title="Opacity 70"
            className="opacity-70"
            value="70%"
            description="High opacity - mostly visible"
        />

        <OpacityExample
            title="Opacity 75"
            className="opacity-75"
            value="75%"
            description="Three-quarter opacity - clearly visible"
        />

        <OpacityExample
            title="Opacity 80"
            className="opacity-80"
            value="80%"
            description="Very high opacity - almost fully visible"
        />

        <OpacityExample
            title="Opacity 90"
            className="opacity-90"
            value="90%"
            description="Nearly opaque - subtle transparency"
        />

        <OpacityExample
            title="Opacity 95"
            className="opacity-95"
            value="95%"
            description="Almost opaque - barely transparent"
        />

        <OpacityExample
            title="Opacity 100"
            className="opacity-100"
            value="100%"
            description="Completely opaque - fully visible"
        />
    </ScrollView>
);

export const OverlayEffects = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Overlay Effects</Text>
        <Text className="text-typography-600 text-sm mb-6">
            Common opacity values used for overlay effects like modals, tooltips, and loading states.
        </Text>

        <OverlayExample
            title="Light Overlay"
            className="opacity-20"
            value="20%"
            description="Subtle overlay for highlighting content"
        />

        <OverlayExample
            title="Medium Overlay"
            className="opacity-40"
            value="40%"
            description="Moderate overlay for modal backgrounds"
        />

        <OverlayExample
            title="Strong Overlay"
            className="opacity-60"
            value="60%"
            description="Strong overlay for focus states"
        />

        <OverlayExample
            title="Heavy Overlay"
            className="opacity-80"
            value="80%"
            description="Heavy overlay for blocking interactions"
        />
    </ScrollView>
);

export const CommonUseCases = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Common Use Cases</Text>

        <View className="mb-6">
            <Text className="text-typography-800 text-lg font-semibold mb-3">Disabled States</Text>
            <View className="flex-row gap-4">
                <View className="bg-primary-500 px-4 py-2 rounded-md">
                    <Text className="text-typography-white font-medium">Enabled</Text>
                </View>
                <View className="bg-primary-500 px-4 py-2 rounded-md opacity-40">
                    <Text className="text-typography-white font-medium">Disabled</Text>
                </View>
            </View>
            <Text className="text-typography-500 text-xs mt-2">
                Disabled elements typically use opacity-40 or opacity-25
            </Text>
        </View>

        <View className="mb-6">
            <Text className="text-typography-800 text-lg font-semibold mb-3">Loading States</Text>
            <View className="bg-background-50 p-4 rounded-lg relative">
                <Text className="text-typography-700 mb-2">Content loading...</Text>
                <View className="h-4 bg-outline-200 rounded mb-2" />
                <View className="h-4 bg-outline-200 rounded w-3/4" />

                <View className="absolute inset-0 bg-background-0 opacity-75 items-center justify-center rounded-lg">
                    <Text className="text-typography-600">Loading...</Text>
                </View>
            </View>
            <Text className="text-typography-500 text-xs mt-2">
                Loading overlays typically use opacity-75 to show content underneath
            </Text>
        </View>

        <View className="mb-6">
            <Text className="text-typography-800 text-lg font-semibold mb-3">Hover Effects</Text>
            <View className="bg-primary-500 p-4 rounded-lg relative">
                <Text className="text-typography-white font-medium">Hover me</Text>
                <View className="absolute inset-0 bg-background-0 opacity-0 rounded-lg" />
            </View>
            <Text className="text-typography-500 text-xs mt-2">
                Hover effects often transition from opacity-0 to opacity-10 or opacity-20
            </Text>
        </View>

        <View className="mb-6">
            <Text className="text-typography-800 text-lg font-semibold mb-3">Watermarks</Text>
            <View className="bg-background-50 p-6 rounded-lg relative">
                <Text className="text-typography-700 text-lg font-bold">Main Content</Text>
                <Text className="text-typography-600">This is the primary content area.</Text>

                <View className="absolute inset-0 items-center justify-center">
                    <Text className="text-typography-500 text-4xl font-bold opacity-10 rotate-12">WATERMARK</Text>
                </View>
            </View>
            <Text className="text-typography-500 text-xs mt-2">
                Watermarks typically use opacity-5 to opacity-15 to be subtle
            </Text>
        </View>

        <View className="mb-6">
            <Text className="text-typography-800 text-lg font-semibold mb-3">Image Overlays</Text>
            <View className="bg-gradient-to-br from-primary-400 to-secondary-600 h-32 rounded-lg relative">
                <View className="absolute inset-0 bg-background-900 opacity-50 rounded-lg" />
                <View className="absolute inset-0 items-center justify-center">
                    <Text className="text-typography-white text-xl font-bold">Overlay Text</Text>
                </View>
            </View>
            <Text className="text-typography-500 text-xs mt-2">
                Image overlays use opacity-30 to opacity-70 for text readability
            </Text>
        </View>
    </ScrollView>
);
