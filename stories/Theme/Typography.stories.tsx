import React from "react";
import {View, Text, ScrollView} from "react-native";

export default {
    title: "Theme/Typography",
    component: View,
};

const TypographyExample = ({
    title,
    className,
    sampleText = "The quick brown fox jumps over the lazy dog",
    description,
}: {
    title: string;
    className: string;
    sampleText?: string;
    description?: string;
}) => (
    <View className="mb-6 p-4 bg-background-50 rounded-lg">
        <View className="flex-row items-center justify-between mb-2">
            <Text className="text-typography-900 font-semibold text-sm">{title}</Text>
            <Text className="text-typography-600 text-xs">{className}</Text>
        </View>
        <Text className={className}>{sampleText}</Text>
        {description && <Text className="text-typography-500 text-xs mt-2">{description}</Text>}
    </View>
);

const FontFamilyExample = ({
    title,
    fontClass,
    description,
}: {
    title: string;
    fontClass: string;
    description?: string;
}) => (
    <View className="mb-6 p-4 bg-background-50 rounded-lg">
        <View className="flex-row items-center justify-between mb-2">
            <Text className="text-typography-900 font-semibold text-sm">{title}</Text>
            <Text className="text-typography-600 text-xs">{fontClass}</Text>
        </View>
        <Text className={`${fontClass} text-lg text-typography-900`}>The quick brown fox jumps over the lazy dog</Text>
        <Text className={`${fontClass} text-base text-typography-700`}>ABCDEFGHIJKLMNOPQRSTUVWXYZ</Text>
        <Text className={`${fontClass} text-base text-typography-700`}>abcdefghijklmnopqrstuvwxyz</Text>
        <Text className={`${fontClass} text-base text-typography-700`}>0123456789 !@#$%^&*()</Text>
        {description && <Text className="text-typography-500 text-xs mt-2">{description}</Text>}
    </View>
);

export const FontSizes = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Font Sizes</Text>

        <TypographyExample
            title="2XS (10px)"
            className="text-2xs text-typography-900"
            description="Extra extra small text - used for fine print"
        />

        <TypographyExample
            title="XS (12px)"
            className="text-xs text-typography-900"
            description="Extra small text - used for captions and labels"
        />

        <TypographyExample
            title="SM (14px)"
            className="text-sm text-typography-900"
            description="Small text - used for secondary information"
        />

        <TypographyExample
            title="Base (16px)"
            className="text-base text-typography-900"
            description="Base text size - used for body text"
        />

        <TypographyExample
            title="LG (18px)"
            className="text-lg text-typography-900"
            description="Large text - used for emphasis"
        />

        <TypographyExample
            title="XL (20px)"
            className="text-xl text-typography-900"
            description="Extra large text - used for subheadings"
        />

        <TypographyExample
            title="2XL (24px)"
            className="text-2xl text-typography-900"
            description="2X large text - used for headings"
        />

        <TypographyExample
            title="3XL (30px)"
            className="text-3xl text-typography-900"
            description="3X large text - used for major headings"
        />

        <TypographyExample
            title="4XL (36px)"
            className="text-4xl text-typography-900"
            description="4X large text - used for display text"
        />

        <TypographyExample
            title="5XL (48px)"
            className="text-5xl text-typography-900"
            description="5X large text - used for hero text"
        />

        <TypographyExample
            title="6XL (60px)"
            className="text-6xl text-typography-900"
            description="6X large text - used for large display text"
        />
    </ScrollView>
);

export const FontFamilies = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Font Families</Text>

        <FontFamilyExample
            title="Jakarta (Plus Jakarta Sans)"
            fontClass="font-jakarta"
            description="Modern sans-serif font - used for UI elements"
        />

        <FontFamilyExample
            title="Roboto"
            fontClass="font-roboto"
            description="Google's Roboto font - used for body text"
        />

        <FontFamilyExample title="Inter" fontClass="font-inter" description="Inter font - optimized for UI" />

        <FontFamilyExample
            title="Source Code Pro"
            fontClass="font-code"
            description="Monospace font - used for code snippets"
        />

        <FontFamilyExample
            title="Space Mono"
            fontClass="font-space-mono"
            description="Monospace font - used for technical content"
        />
    </ScrollView>
);

export const FontWeights = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Font Weights</Text>

        <TypographyExample
            title="Thin (100)"
            className="font-thin text-lg text-typography-900"
            description="Thinnest font weight"
        />

        <TypographyExample
            title="Extra Light (200)"
            className="font-extralight text-lg text-typography-900"
            description="Extra light font weight"
        />

        <TypographyExample
            title="Light (300)"
            className="font-light text-lg text-typography-900"
            description="Light font weight"
        />

        <TypographyExample
            title="Normal (400)"
            className="font-normal text-lg text-typography-900"
            description="Normal font weight - default"
        />

        <TypographyExample
            title="Medium (500)"
            className="font-medium text-lg text-typography-900"
            description="Medium font weight"
        />

        <TypographyExample
            title="Semibold (600)"
            className="font-semibold text-lg text-typography-900"
            description="Semibold font weight"
        />

        <TypographyExample
            title="Bold (700)"
            className="font-bold text-lg text-typography-900"
            description="Bold font weight - used for emphasis"
        />

        <TypographyExample
            title="Extra Bold (800)"
            className="font-extrabold text-lg text-typography-900"
            description="Extra bold font weight"
        />

        <TypographyExample
            title="Black (900)"
            className="font-black text-lg text-typography-900"
            description="Blackest font weight"
        />

        <TypographyExample
            title="Extra Black (950)"
            className="font-extrablack text-lg text-typography-900"
            description="Custom extra black font weight"
        />
    </ScrollView>
);

export const TextColors = () => (
    <ScrollView className="flex-1 p-4 bg-background-0">
        <Text className="text-typography-900 text-2xl font-bold mb-6">Text Colors</Text>

        <TypographyExample
            title="Typography 950 (Darkest)"
            className="text-typography-950 text-lg"
            description="Darkest text color - used for primary headings"
        />

        <TypographyExample
            title="Typography 900"
            className="text-typography-900 text-lg"
            description="Very dark text - used for headings"
        />

        <TypographyExample
            title="Typography 800"
            className="text-typography-800 text-lg"
            description="Dark text - used for body text"
        />

        <TypographyExample
            title="Typography 700"
            className="text-typography-700 text-lg"
            description="Main text color - default body text"
        />

        <TypographyExample
            title="Typography 600"
            className="text-typography-600 text-lg"
            description="Medium text - used for secondary text"
        />

        <TypographyExample
            title="Typography 500"
            className="text-typography-500 text-lg"
            description="Light text - used for muted text"
        />

        <TypographyExample
            title="Typography 400"
            className="text-typography-400 text-lg"
            description="Lighter text - used for placeholders"
        />

        <TypographyExample
            title="Typography 300"
            className="text-typography-300 text-lg"
            description="Very light text - used for disabled text"
        />
    </ScrollView>
);
