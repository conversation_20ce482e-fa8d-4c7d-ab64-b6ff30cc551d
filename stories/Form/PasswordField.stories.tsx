import React from "react";
import {View} from "react-native";
import {useForm, FormProvider} from "react-hook-form";
import {PasswordField} from "@/components/Form/PasswordField";

export default {
    title: "Form/PasswordField",
    component: PasswordField,
};

const FieldShell: React.FC<{defaultValues?: any; children: React.ReactNode}> = ({
    defaultValues = {password: ""},
    children,
}) => {
    const methods = useForm({defaultValues});
    return (
        <FormProvider {...methods}>
            <View style={{padding: 20}}>{children}</View>
        </FormProvider>
    );
};

export const Empty = () => (
    <FieldShell>
        <PasswordField />
    </FieldShell>
);

export const Prefilled = () => (
    <FieldShell defaultValues={{password: "secret123"}}>
        <PasswordField />
    </FieldShell>
);

export const WithHelperText = () => (
    <FieldShell>
        <PasswordField helperText="At least 6 characters" />
    </FieldShell>
);

export const WithError = () => {
    const methods = useForm({defaultValues: {password: ""}});

    React.useEffect(() => {
        methods.setError("password", {
            type: "manual",
            message: "Password is too short",
        });
    }, [methods]);

    return (
        <FormProvider {...methods}>
            <View style={{padding: 20}}>
                <PasswordField />
            </View>
        </FormProvider>
    );
};
