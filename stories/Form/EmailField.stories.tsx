import React from "react";
import {View} from "react-native";
import {useForm, FormProvider} from "react-hook-form";
import {EmailField} from "@/components/Form/EmailField";

export default {
    title: "Form/EmailField",
    component: EmailField,
};

const FieldShell: React.FC<{defaultValues?: any; children: React.ReactNode}> = ({
    defaultValues = {email: ""},
    children,
}) => {
    const methods = useForm({defaultValues});
    return (
        <FormProvider {...methods}>
            <View style={{padding: 20}}>{children}</View>
        </FormProvider>
    );
};

export const Empty = () => (
    <FieldShell>
        <EmailField />
    </FieldShell>
);

export const Prefilled = () => (
    <FieldShell defaultValues={{email: "<EMAIL>"}}>
        <EmailField />
    </FieldShell>
);

export const WithError = () => {
    const methods = useForm({defaultValues: {email: ""}});

    React.useEffect(() => {
        methods.setError("email", {
            type: "manual",
            message: "Invalid email address",
        });
    }, [methods]);

    return (
        <FormProvider {...methods}>
            <View style={{padding: 20}}>
                <EmailField />
            </View>
        </FormProvider>
    );
};
