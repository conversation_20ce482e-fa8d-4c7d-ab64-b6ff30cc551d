import React from "react";
import {Alert, View} from "react-native";
import {action} from "@storybook/addon-actions";
import {LoginForm, type LoginFormValues} from "@/components/Form/LoginForm";

export default {
    title: "Form/LoginForm",
    component: LoginForm,
};

const onForgot = () => {
    action("onForgotPasswordPress")();
    Alert.alert("Forgot password", "Link/reset flow here");
};

export const Default = () => {
    const onSubmit = async (values: LoginFormValues) => {
        action("onSubmit")(values);
        await new Promise(r => setTimeout(r, 1200));
        Alert.alert("Submitted", JSON.stringify(values, null, 2));
    };

    return (
        <View style={{padding: 20}}>
            <LoginForm onSubmit={onSubmit} onForgotPasswordPress={onForgot} />
        </View>
    );
};

export const DisabledWhileSubmitting = () => {
    const onSubmit = async (values: LoginFormValues) => {
        action("onSubmit (slow)")(values);
        await new Promise(r => setTimeout(r, 3000));
    };

    return (
        <View style={{padding: 20}}>
            <LoginForm onSubmit={onSubmit} onForgotPasswordPress={onForgot} />
        </View>
    );
};
