import {markdownStyles} from "@/styles/markdown.styled";
import {markdownDarkStyles} from "@/styles/markdown_dark.styled";
import {useColorScheme} from "nativewind";
import {Image} from "react-native";
import Markdown, {MarkdownIt} from "react-native-markdown-display";

// Create a single MarkdownIt instance (avoid recreating on every render)
const md = MarkdownIt({typographer: true});

// Override image renderer to avoid spreading `key` via props
const rules = {
    image: (node: any, _children: any, _parent: any, styles: any) => {
        const src = node?.attributes?.src ?? "";
        const alt = node?.attributes?.alt ?? "";
        return (
            <Image
                key={node.key}
                source={{uri: src}}
                accessibilityLabel={alt}
                style={styles?.image}
                resizeMode="contain"
            />
        );
    },
};

interface Props {
    content: string;
}

export const MarkdownCustom = ({content}: Props) => {
    const {colorScheme} = useColorScheme();
    const styles = colorScheme !== "dark" ? markdownStyles : markdownDarkStyles;

    return (
        <Markdown style={styles} markdownit={md} rules={rules}>
            {content}
        </Markdown>
    );
};
