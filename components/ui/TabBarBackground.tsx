import React from "react";
import {StyleSheet, View} from "react-native";

// Cross-platform background for the bottom tab bar.
// Uses NativeWind classes that react to GluestackUIProvider theme (via CSS vars).
export default function TabBarBackground() {
    return (
        <View
            // Semi-transparent background that adapts to theme tokens
            className="bg-background-suplement1 border-none"
            style={StyleSheet.absoluteFill}
        />
    );
}

// Keep the same API as iOS module for parity
export function useBottomTabOverflow() {
    return 0;
}
