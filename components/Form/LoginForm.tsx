import {VStack} from "@/components/ui/vstack";
import {Heading} from "@/components/ui/heading";
import {EmailField} from "@/components/Form/EmailField";
import {PasswordField} from "@/components/Form/PasswordField";
import {Button, ButtonText} from "@/components/ui/button";
import React from "react";
import {FormProvider, useForm} from "react-hook-form";
import {z} from "zod";
import {zodResolver} from "@hookform/resolvers/zod";

const loginSchema = z.object({
    email: z.email("Please enter a valid email address."),
    password: z.string().min(6, "Password must be at least 6 characters."),
});

export type LoginFormValues = z.infer<typeof loginSchema>;

type Props = {
    onSubmit: (values: LoginFormValues) => void | Promise<void>;
    onForgotPasswordPress: () => void;
};

export const LoginForm = ({onSubmit, onForgotPasswordPress}: Props) => {
    const methods = useForm<LoginFormValues>({
        resolver: zod<PERSON><PERSON><PERSON>ver(loginSchema),
        defaultValues: {email: "", password: ""},
        mode: "onSubmit",
    });

    const handleSubmit = methods.handleSubmit(onSubmit);

    return (
        <FormProvider {...methods}>
            <VStack className="gap-4 w-full">
                <Heading className="text-center font-medium" size="xl">
                    Log in with email
                </Heading>

                <EmailField />
                <PasswordField />

                <Button
                    className="mt-3 rounded-lg"
                    onPress={handleSubmit}
                    variant="solid"
                    size="xl"
                    isDisabled={methods.formState.isSubmitting}
                >
                    <ButtonText>Log in</ButtonText>
                </Button>

                <Button onPress={onForgotPasswordPress} variant="link">
                    <ButtonText className="text-typography-800 font-semibold">Forgot password?</ButtonText>
                </Button>
            </VStack>
        </FormProvider>
    );
};
