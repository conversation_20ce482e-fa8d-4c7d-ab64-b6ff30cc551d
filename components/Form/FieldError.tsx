import {FormControlError, FormControlErrorIcon, FormControlErrorText} from "@/components/ui/form-control";
import {AlertCircleIcon} from "@/components/ui/icon";

type Props = {
    message: string | undefined;
};

/**
 * This component is used to display error messages for form fields.
 */
export const FormFieldError = ({message}: Props) => {
    return (
        <FormControlError>
            <FormControlErrorIcon as={AlertCircleIcon} className="text-indicator-error" size="xs" />
            <FormControlErrorText className="text-indicator-error" size="xs">
                {message}
            </FormControlErrorText>
        </FormControlError>
    );
};
