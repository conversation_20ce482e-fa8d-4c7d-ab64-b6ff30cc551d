import {FormControl, FormControlLabelText} from "@/components/ui/form-control";
import {Input, InputField, InputIcon, InputSlot} from "@/components/ui/input";
import React from "react";
import {styles} from "./styles";

import {FormFieldError} from "@/components/Form/FieldError";
import {useController, useFormContext} from "react-hook-form";
import {EmailIcon} from "../ui/icon";

type Props = {
    name?: string;
    label?: string;
    placeholder?: string;
    required?: boolean;
};

export const EmailField = ({name = "email", label = "Email", placeholder = "Enter email", required = true}: Props) => {
    const {control} = useFormContext();
    const {field, fieldState} = useController({control, name});

    return (
        <FormControl isInvalid={!!fieldState.error} className={styles.classes.container}>
            <FormControlLabelText className={styles.classes.label} size="sm">
                {label}
                {required ? "*" : ""}
            </FormControlLabelText>

            <Input size="xl" className={styles.classes.input} isRequired={required}>
                <InputField
                    type="text"
                    placeholder={placeholder}
                    value={field.value ?? ""}
                    onChangeText={field.onChange}
                    onBlur={field.onBlur}
                    keyboardType="email-address"
                    textContentType="emailAddress"
                    autoCapitalize="none"
                    style={{fontSize: 16}}
                />
                <InputSlot className={styles.classes.inputSlot}>
                    <InputIcon as={EmailIcon} className="fill-background-600 stroke-none h-4 w-4" />
                </InputSlot>
            </Input>

            {fieldState.error ? <FormFieldError message={fieldState.error.message} /> : null}
        </FormControl>
    );
};
