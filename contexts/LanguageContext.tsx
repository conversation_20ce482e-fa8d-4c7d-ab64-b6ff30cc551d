import AsyncStorage from "@react-native-async-storage/async-storage";
import React, {createContext, useContext, useState} from "react";
import {useTranslation} from "react-i18next";

type LanguageContextType = {
    language: string;
    setLanguage: (language: string) => void;
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{children: React.ReactNode}> = ({children}) => {
    const {i18n} = useTranslation();
    const [language, setLanguageState] = useState(i18n.language);

    const setLanguage = async (lang: string) => {
        await i18n.changeLanguage(lang);
        setLanguageState(lang);
        AsyncStorage.setItem("user-language", lang);
    };

    return <LanguageContext.Provider value={{language, setLanguage}}>{children}</LanguageContext.Provider>;
};

export const useLanguage = () => {
    const context = useContext(LanguageContext);
    if (!context) {
        throw new Error("useLanguage must be used within a LanguageProvider");
    }
    return context;
};
