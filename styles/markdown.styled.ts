import {StyleSheet} from "react-native";

export const markdownStyles = StyleSheet.create({
    heading1: {
        fontSize: 18,
        letterSpacing: 0.5,
        marginBottom: 18,
    },
    heading2: {
        fontSize: 16,
        letterSpacing: 0.5,
        marginVertical: 10,
    },

    // Lists
    bullet_list: {},
    ordered_list: {},
    list_item: {
        marginBottom: 10,
        letterSpacing: 0.5,
        lineHeight: 24,
        fontSize: 15,
        flexDirection: "row",
        justifyContent: "flex-start",
    },
    // @pseudo class, does not have a unique render rule
    bullet_list_icon: {
        marginLeft: 10,
        marginRight: 10,
    },
    // @pseudo class, does not have a unique render rule
    bullet_list_content: {
        flex: 1,
    },
    // @pseudo class, does not have a unique render rule
    ordered_list_icon: {
        marginLeft: 10,
        marginRight: 10,
    },
    // @pseudo class, does not have a unique render rule
    ordered_list_content: {
        flex: 1,
    },
    paragraph: {
        letterSpacing: 0.5,
        lineHeight: 24,
        fontSize: 15,
        marginTop: 10,
        marginBottom: 10,
        flexWrap: "wrap",
        flexDirection: "row",
        alignItems: "flex-start",
        justifyContent: "flex-start",
        width: "100%",
    },
});
