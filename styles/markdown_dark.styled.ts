import {StyleSheet} from "react-native";

export const markdownDarkStyles = StyleSheet.create({
    heading1: {
        fontSize: 18,
        letterSpacing: 0.5,
        marginBottom: 18,
        color: "#FFFFFF", // biały na ciemnym tle
    },
    heading2: {
        fontSize: 16,
        letterSpacing: 0.5,
        marginVertical: 10,
        color: "#E0E0E0", // lekko <PERSON>, mniej kontrastowy niż heading1
    },

    // Lists
    bullet_list: {},
    ordered_list: {},
    list_item: {
        marginBottom: 10,
        letterSpacing: 0.5,
        lineHeight: 24,
        fontSize: 15,
        flexDirection: "row",
        justifyContent: "flex-start",
        color: "#FFFFFF", // tekst listy
    },
    bullet_list_icon: {
        marginLeft: 10,
        marginRight: 10,
        color: "#FFD700", // np. złoty punkt dla listy punktowanej
    },
    bullet_list_content: {
        flex: 1,
    },
    ordered_list_icon: {
        marginLeft: 10,
        marginRight: 10,
        color: "#FFA500", // np. pomarańczowy numer
    },
    ordered_list_content: {
        flex: 1,
    },
    paragraph: {
        letterSpacing: 0.5,
        lineHeight: 24,
        fontSize: 15,
        marginTop: 10,
        marginBottom: 10,
        flexWrap: "wrap",
        flexDirection: "row",
        alignItems: "flex-start",
        justifyContent: "flex-start",
        width: "100%",
        color: "#E0E0E0", // standardowy tekst akapitów
    },
});
