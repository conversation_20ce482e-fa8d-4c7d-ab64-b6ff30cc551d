import {LinkList, LinkListItem} from "@/components/LinkList";
import {useLegalData} from "@/hooks/useLegalData";
import {View} from "react-native";

export default function AccountScreen() {
    const LegalAndPrivacy = useLegalData();

    return (
        <View className="flex-1 items-start">
            <LinkList>
                {LegalAndPrivacy.content.map((item, index) => (
                    <LinkListItem key={`legal-setting-${item.title}-${index}`} {...item} />
                ))}
            </LinkList>
        </View>
    );
}
