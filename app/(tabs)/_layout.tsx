import {Tabs} from "expo-router";
import {Platform} from "react-native";

import {AccountIcon, CompasIcon, Icon, OrganizerIcon} from "@/components/ui/icon";
import TabBarBackground from "@/components/ui/TabBarBackground";
import {Text} from "@/components/ui/text";
import clsx from "clsx";

export default function TabLayout() {
    return (
        <Tabs
            initialRouteName="explore"
            screenOptions={{
                headerShown: false,
                tabBarBackground: TabBarBackground,
                // Make tab scenes transparent so root background shows
                sceneStyle: {backgroundColor: "transparent"},
                tabBarStyle: Platform.select({
                    ios: {
                        // Use a transparent background on iOS to show the blur effect
                        position: "absolute",
                        backgroundColor: "transparent",
                        borderTopWidth: 0,
                        borderTopColor: "transparent",
                        // Remove iOS shadow line
                        shadowColor: "transparent",
                        shadowOpacity: 0,
                        shadowRadius: 0,
                    },
                    android: {
                        // Remove Android elevation/shadow line
                        elevation: 0,
                        backgroundColor: "transparent",
                        borderTopWidth: 0,
                    },
                    default: {
                        backgroundColor: "transparent",
                        borderTopWidth: 0,
                    },
                }),
            }}
        >
            <Tabs.Screen
                name="explore"
                options={{
                    title: "Explore",
                    tabBarIcon: ({focused}) => (
                        <Icon
                            as={CompasIcon}
                            size="xl"
                            className={focused ? "fill-background-900 stroke-none" : "fill-background-500 stroke-none"}
                        />
                    ),
                    tabBarLabel: ({focused}) => (
                        <Text className={clsx("text-xs", focused ? "text-typography-950" : "text-typography-600")}>
                            Explore
                        </Text>
                    ),
                }}
            />
            <Tabs.Screen
                name="organizer"
                options={{
                    title: "Organizer",
                    tabBarIcon: ({focused}) => (
                        <Icon
                            as={OrganizerIcon}
                            size="xl"
                            className={focused ? "fill-background-900 stroke-none" : "fill-background-500 stroke-none"}
                        />
                    ),
                    tabBarLabel: ({focused}) => (
                        <Text className={clsx("text-xs", focused ? "text-typography-950" : "text-typography-600")}>
                            Organizer
                        </Text>
                    ),
                }}
            />
            <Tabs.Screen
                name="account"
                options={{
                    title: "Account",
                    href: "/account",
                    tabBarIcon: ({focused}) => (
                        <Icon
                            as={AccountIcon}
                            size="xl"
                            className={focused ? "fill-background-900 stroke-none" : "fill-background-500 stroke-none"}
                        />
                    ),
                    tabBarLabel: ({focused}) => (
                        <Text className={clsx("text-xs", focused ? "text-typography-950" : "text-typography-600")}>
                            Account
                        </Text>
                    ),
                }}
            />
        </Tabs>
    );
}
