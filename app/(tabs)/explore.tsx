import {Button, ButtonText} from "@/components/ui/button";
import {Paths} from "@/enum/Paths";
import {useRouter} from "expo-router";
import {StyleSheet, Text, View} from "react-native";

export default function ExploreScreen() {
    const router = useRouter();

    const handleRedirectToLogin = () => {
        router.push(Paths.LOGIN);
    };

    return (
        <View style={styles.container}>
            <Button onPress={handleRedirectToLogin}>
                <ButtonText>Login</ButtonText>
            </Button>
            <Text className="text-typography-400">Explore</Text>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
    },
});
