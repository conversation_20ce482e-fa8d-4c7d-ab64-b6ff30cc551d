import {useFonts} from "expo-font";
import {Stack} from "expo-router";
import "react-native-reanimated";

import {GluestackUIProvider} from "@/components/ui/gluestack-ui-provider";
import {LanguageProvider} from "@/contexts/LanguageContext";
import "@/global.css";
import {NativeStackNavigationOptions} from "@react-navigation/native-stack";
import {View} from "react-native";
import {SafeAreaProvider} from "react-native-safe-area-context";

const authStackScreenOptions: NativeStackNavigationOptions = {
    headerShown: false,
    presentation: "transparentModal",
    animation: "fade",
    contentStyle: {backgroundColor: "transparent"},
    gestureEnabled: true,
};

export default function RootLayout() {
    const [loaded] = useFonts({
        SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
    });

    if (!loaded) {
        // Async font loading only occurs in development.
        return null;
    }

    return (
        <GluestackUIProvider mode="light">
            <LanguageProvider>
                <SafeAreaProvider>
                    <View className="flex-1 bg-background-suplement2">
                        <Stack
                            screenOptions={{
                                // Make screens transparent so the global bg shows through
                                contentStyle: {backgroundColor: "transparent"},
                            }}
                        >
                            <Stack.Screen name="(tabs)" options={{headerShown: false}} />
                            <Stack.Screen name="(auth)" options={authStackScreenOptions} />
                            <Stack.Screen name="+not-found" />
                        </Stack>
                    </View>
                </SafeAreaProvider>
            </LanguageProvider>
        </GluestackUIProvider>
    );
}
