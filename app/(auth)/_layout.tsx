import {Stack} from "expo-router";
import {NativeStackNavigationOptions} from "@react-navigation/native-stack";

const stackScreenOptions: NativeStackNavigationOptions = {
    contentStyle: {
        backgroundColor: "transparent",
    },
};

export default function AuthLayout() {
    return (
        <Stack screenOptions={stackScreenOptions}>
            <Stack.Screen name="login" options={{headerShown: false}} />
            {/*<Stack.Screen name="register" options={{headerShown: false}}/>*/}
        </Stack>
    );
}
