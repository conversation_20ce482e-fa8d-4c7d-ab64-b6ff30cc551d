/* Semantic colors - Dark */
module.exports = {
    theme: {
        extend: {
            colors: {
                "Primary-primary0": "#A6A6A6FF",
                "Primary-primary50": "#AFAFAFFF",
                "Primary-primary100": "#BABABAFF",
                "Primary-primary200": "#C5C5C5FF",
                "Primary-primary300": "#D4D4D4FF",
                "Primary-primary400": "#DDDDDDFF",
                "Primary-primary500": "#E6E6E6FF",
                "Primary-primary600": "#F0F0F0FF",
                "Primary-primary700": "#FAFAFAFF",
                "Primary-primary800": "#FDFDFDFF",
                "Primary-primary900": "#FEF9F9FF",
                "Primary-primary950": "#FDFCFCFF",
                "Secondary-secondary0": "#141414FF",
                "Secondary-secondary50": "#171717FF",
                "Secondary-secondary100": "#1F1F1FFF",
                "Secondary-secondary200": "#272727FF",
                "Secondary-secondary300": "#2C2C2CFF",
                "Secondary-secondary400": "#383939FF",
                "Secondary-secondary500": "#3F4040FF",
                "Secondary-secondary600": "#565656FF",
                "Secondary-secondary700": "#656565FF",
                "Secondary-secondary800": "#878787FF",
                "Secondary-secondary900": "#969696FF",
                "Secondary-secondary950": "#A4A4A4FF",
                "Tertiary-tertiary0": "#543112FF",
                "Tertiary-tertiary50": "#6C3D13FF",
                "Tertiary-tertiary100": "#824917FF",
                "Tertiary-tertiary200": "#B4621AFF",
                "Tertiary-tertiary300": "#D7751FFF",
                "Tertiary-tertiary400": "#E78128FF",
                "Tertiary-tertiary500": "#FB9D4BFF",
                "Tertiary-tertiary600": "#FDB474FF",
                "Tertiary-tertiary700": "#FED1AAFF",
                "Tertiary-tertiary800": "#FFE9D5FF",
                "Tertiary-tertiary900": "#FFF2E5FF",
                "Tertiary-tertiary950": "#FFFAF5FF",
                "Typography-typography0": "#171717FF",
                "Typography-typography50": "#262627FF",
                "Typography-typography100": "#404040FF",
                "Typography-typography200": "#525252FF",
                "Typography-typography300": "#737373FF",
                "Typography-typography400": "#8C8C8CFF",
                "Typography-typography500": "#A3A3A3FF",
                "Typography-typography600": "#D4D4D4FF",
                "Typography-typography700": "#DBDBDCFF",
                "Typography-typography800": "#E5E5E5FF",
                "Background-background-suplement-1": "#1F1F1FFF",
                "Typography-typography900": "#F5F5F5FF",
                "Background-background-suplement-2": "#141414FF",
                "Typography-typography950": "#FEFEFFFF",
                "Background-background-full": "#000000FF",
                "Background-background0": "#121212FF",
                "Background-background50": "#272727FF",
                "Background-background100": "#414141FF",
                "Background-background200": "#535353FF",
                "Background-background300": "#747474FF",
                "Background-background400": "#8E8E8EFF",
                "Background-background500": "#A3A3A3FF",
                "Background-background600": "#D5D4D4FF",
                "Background-background700": "#E5E5E5FF",
                "Background-background800": "#F2F2F2FF",
                "Background-background900": "#F6F6F6FF",
                "Background-background950": "#FFFFFFFF",
                "Background-background-muted": "#333333FF",
                "Border-border0": "#1A1717FF",
                "Border-border50": "#272624FF",
                "Border-border100": "#414141FF",
                "Border-border200": "#535252FF",
                "Border-border300": "#737474FF",
                "Border-border400": "#8C8D8DFF",
                "Border-border500": "#A5A3A3FF",
                "Border-border600": "#D3D3D3FF",
                "Border-border700": "#DDDCDBFF",
                "Border-border800": "#E6E6E6FF",
                "Border-border900": "#F3F3F3FF",
                "Border-border950": "#FDFEFEFF",
                "Success-success0": "#1B3224FF",
                "Success-success50": "#14532DFF",
                "Success-success100": "#166534FF",
                "Success-success200": "#206F3EFF",
                "Success-success300": "#2A7948FF",
                "Success-success400": "#348352FF",
                "Success-success500": "#489766FF",
                "Success-success600": "#66B584FF",
                "Success-success700": "#84D3A2FF",
                "Success-success800": "#A2F1C0FF",
                "Success-success900": "#CAFFE8FF",
                "Success-success950": "#E4FFF4FF",
                "Success-success-background": "#1C2B21FF",
                "Error-error0": "#531313FF",
                "Error-error50": "#7F1D1DFF",
                "Error-error100": "#991B1BFF",
                "Error-error200": "#B91C1CFF",
                "Error-error300": "#DC2626FF",
                "Error-error400": "#E63535FF",
                "Error-error500": "#EF4444FF",
                "Error-error600": "#F96160FF",
                "Error-error700": "#E55B5AFF",
                "Error-error800": "#FECACAFF",
                "Error-error900": "#FEE2E2FF",
                "Error-error950": "#FEE9E9FF",
                "Error-error-background": "#422B2BFF",
                "Warning-warning0": "#542D12FF",
                "Warning-warning50": "#6C3813FF",
                "Warning-warning100": "#824417FF",
                "Warning-warning200": "#B45A1AFF",
                "Warning-warning300": "#D76C1FFF",
                "Warning-warning400": "#E77828FF",
                "Warning-warning500": "#FB954BFF",
                "Warning-warning600": "#FDAD74FF",
                "Warning-warning700": "#FECDAAFF",
                "Warning-warning800": "#FFE7D5FF",
                "Warning-warning900": "#FFF4EDFF",
                "Warning-warning950": "#FFF9F5FF",
                "Warning-warning-background": "#412F23FF",
                "Info-info0": "#032638FF",
                "Indicator-Indicator-primary": "#F7F7F7FF",
                "Indicator-Indicator-info": "#A1C7F5FF",
                "Info-info50": "#05405DFF",
                "Indicator-Indicator-error": "#E84645FF",
                "Info-info100": "#075A83FF",
                "Info-info200": "#0973A8FF",
                "Info-info300": "#0B8DCDFF",
                "Info-info400": "#0DA6F2FF",
                "Info-info500": "#32B4F4FF",
                "Info-info600": "#57C2F6FF",
                "Info-info700": "#7CCFF8FF",
                "Info-info800": "#A2DDFAFF",
                "Info-info900": "#C7EBFCFF",
                "Info-Info950": "#ECF8FEFF",
                "Info-info-background": "#1A282EFF",
            },
            spacing: {
                "Spacing-0": "0px",
                "Spacing-px": "1px",
                "Spacing-0point5": "2px",
                "Spacing-1": "4px",
                "Spacing-1point5": "6px",
                "Spacing-2": "8px",
                "Spacing-2point5": "10px",
                "Spacing-3": "12px",
                "Spacing-3point5": "14px",
                "Spacing-4": "16px",
                "Spacing-4point5": "18px",
                "Spacing-5": "20px",
                "Spacing-6": "24px",
                "Spacing-7": "28px",
                "Spacing-8": "32px",
                "Spacing-9": "36px",
                "Spacing-10": "40px",
                "Spacing-11": "44px",
                "Spacing-12": "48px",
                "Spacing-16": "64px",
                "Spacing-20": "80px",
                "Spacing-24": "96px",
                "Spacing-32": "128px",
                "Spacing-40": "160px",
                "Spacing-48": "192px",
                "Spacing-56": "224px",
                "Spacing-64": "256px",
                "Spacing-72": "288px",
                "Spacing-80": "320px",
                "Spacing-96": "384px",
                "Border-widths-0": "0px",
                "Border-widths-1": "1px",
                "Border-widths-2": "2px",
                "Border-widths-4": "4px",
                "Border-widths-8": "8px",
                "Border-radius-none": "0px",
                "Border-radius-xs": "2px",
                "Border-radius-sm": "4px",
                "Border-radius-md": "6px",
                "Border-radius-lg": "8px",
                "Border-radius-xl": "12px",
                "Border-radius-2xl": "16px",
                "Border-radius-3xl": "24px",
                "Border-radius-full": "9999px",
                "Opacity-0": "0px",
                "Opacity-5": "5px",
                "Opacity-10": "10px",
                "Opacity-20": "20px",
                "Opacity-25": "25px",
                "Opacity-30": "30px",
                "Opacity-40": "40px",
                "Opacity-50": "50px",
                "Opacity-60": "60px",
                "Opacity-70": "70px",
                "Opacity-75": "75px",
                "Opacity-80": "80px",
                "Opacity-90": "90px",
                "Opacity-95": "95px",
                "Opacity-100": "100px",
            },
        },
    },
};
